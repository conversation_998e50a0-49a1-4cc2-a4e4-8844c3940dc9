#%%
# ============================================================================
# 1. IMPORT LIBRARIES AND SET UP ENVIRONMENT
# ============================================================================

# Standard scientific computing libraries
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import pandas as pd
import os

# PyTorch for deep learning
import torch
import torch.nn as nn
import torch.nn.functional as F

# Scipy for signal processing and interpolation
from scipy.ndimage import gaussian_filter
from scipy.interpolate import interp1d

# Custom utility modules for neural data analysis
from utils.loss import PoissonBPSAggregator
from utils.modules import SplitRelu, NonparametricReadout, StackedConv2d
from utils.datasets import DictDataset, generate_gaborium_dataset, generate_gratings_dataset
from utils.general import set_seeds, ensure_tensor
from utils.grid_sample import grid_sample_coords
from utils.rf import calc_sta, plot_stas
from utils.exp.general import get_clock_functions, get_trial_protocols
from utils.exp.dots import dots_rf_map_session
from utils.spikes import KilosortResults

# Matplotlib utilities
from matplotlib.patches import Circle

# ============================================================================
# CONFIGURATION AND SETUP
# ============================================================================

# Set random seeds for reproducibility across runs
RANDOM_SEED = 1002
set_seeds(RANDOM_SEED)

# Configure PyTorch for optimal performance
torch.set_float32_matmul_precision('medium')  # Trade precision for speed

# Set device for computation (GPU if available, otherwise CPU)
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"Using device: {device}")

# Define data directory path
data_dir = Path('/home/<USER>/YatesShifterExample/data')

#%%
# ============================================================================
# 2. LOAD SHIFTED DATASET
# ============================================================================

# Load experimental metadata and stimulus information
f_dset = data_dir / 'gaborium_corrected.dset'

dataset = DictDataset.load(f_dset)
dataset['stim'] = dataset['stim'].float()
dataset['stim'] = (dataset['stim'] - dataset['stim'].mean()) / 255

print(dataset)
n_lags = 20

#%%

# stas = calc_sta(dataset['stim'], dataset['robs'], 
#                 n_lags,device='cuda', batch_size=10000,
#                 progress=True).cpu().numpy()

# plot_stas(stas[:, :, None, :, :])
# #%%
# stes = calc_sta(dataset['stim'], dataset['robs'], 
#                 n_lags,device='cuda', batch_size=10000,
#                 stim_modifier=lambda x: x**2, progress=True).cpu().numpy()

# stes -= stes.mean(axis=(1, 2,3), keepdims=True)

# plot_stas(stes[:, :, None, :, :])

#%%
def get_inds(dset, n_lags):
    dpi_valid = dset['dpi_valid']
    new_trials = torch.diff(dset['trial_inds'], prepend=torch.tensor([-1])) != 0
    dfs = ~new_trials
    dfs &= (dpi_valid > 0)

    for iL in range(n_lags):
        dfs &= torch.roll(dfs, iL)
    
    dfs = dfs.float()
    dfs = dfs[:, None]
    return dfs

n_units = dataset['robs'].shape[1]
n_y, n_x = dataset['stim'].shape[1:3]
dataset['dfs'] = get_inds(dataset, n_lags)
gaborium_inds = dataset['dfs'].squeeze().nonzero(as_tuple=True)[0]
#%%

from utils.datasets import CombinedEmbeddedDataset

keys_lags = {
    'robs': 0,
    'stim': np.arange(n_lags),
}

train_val_split = 0.8


def split_inds_by_trial(dset, inds, train_val_split, seed=1002):
    '''
    Split indices by trial into training and validation sets.

    Parameters
    ----------
    dset : DictDataset
        The dataset containing trial indices.
    inds : torch.Tensor
        The indices to split.
    train_val_split : float
        The fraction of indices to use for training.
    seed : int, optional
        The random seed. The default is 1002.

    Returns
    -------
    train_inds : torch.Tensor
        The indices for training.
    val_inds : torch.Tensor
        The indices for validation.
    '''

    set_seeds(seed)
    trials = dset['trial_inds'].unique()
    rand_trials = torch.randperm(len(trials))
    train_trials = trials[rand_trials[:int(len(trials) * train_val_split)]]
    train_inds = inds[torch.isin(dset['trial_inds'][inds], train_trials)]
    val_trials = trials[rand_trials[int(len(trials) * train_val_split):]]
    val_inds = inds[torch.isin(dset['trial_inds'][inds], val_trials)]
    return train_inds, val_inds


gaborium_train_inds, gaborium_val_inds = split_inds_by_trial(dataset, gaborium_inds, train_val_split, seed=1002)
print(f'Gaborium sample split: {len(gaborium_train_inds) / len(gaborium_inds):.3f} train, {len(gaborium_val_inds) / len(gaborium_inds):.3f} val')

train_data = CombinedEmbeddedDataset(dataset,
                                    gaborium_train_inds,
                                    keys_lags,
                                    'cpu')

val_data = CombinedEmbeddedDataset(dataset,
                                   gaborium_val_inds,
                                   keys_lags,
                                   'cpu')

#%%
batch_size = 128

train_loader = torch.utils.data.DataLoader(train_data, batch_size=batch_size, shuffle=True, num_workers=os.cpu_count()//2)
val_loader = torch.utils.data.DataLoader(val_data, batch_size=batch_size, shuffle=False, num_workers=os.cpu_count()//2)

#%%
batch = next(iter(train_loader))

for k, v in batch.items():
    print(k, v.shape)
# %%

class SpatiotemporalCNNModel(nn.Module):
    '''
    A spatiotemporal cnn model with time only in the first layer.
    '''

    def __init__(self, n_lags, n_y, n_x, n_units, channels, activations, kernel_sizes):
        super(SpatiotemporalCNNModel, self).__init__()

        self.temporal_layer = nn.Conv2d(n_lags, channels[0], kernel_size=1, bias=False) 
        self.layers = nn.ModuleList()
        for iC in range(len(channels) - 1):
            self.layers.append(nn.Conv2d(channels[iC], channels[iC+1], kernel_size=kernel_sizes[iC], bias=False))
            self.layers.append(activations[iC])

        contraction = np.sum(kernel_sizes) - len(kernel_sizes) 
        output_dims = [channels[-1], n_y - contraction, n_x - contraction]
        self.readout = NonparametricReadout(output_dims, n_units, bias=True)


    def forward(self, x, debug=False):
        x = x['stim']
        if debug:
            print(f'Input: {x.shape}')
        x = self.temporal_layer(x)
        if debug:
            print(f'Temporal: {x.shape}')

        for layer in self.layers:
            x = layer(x)
            if debug:
                print(f'Layer {layer}: {x.shape}')
        x = self.readout(x)
        if debug:
            print(f'Readout: {x.shape}')
        rhat = F.softplus(x)
        return rhat

_, n_y, n_x = dataset['stim'].shape
n_units = dataset['robs'].shape[1]
channels = [8, 32]
activations = [nn.Identity(), ]
kernel_sizes = [32, ]

model = SpatiotemporalCNNModel(n_lags, n_y, n_x, n_units, channels, activations, kernel_sizes)

rhat = model(batch, True)

# %%





